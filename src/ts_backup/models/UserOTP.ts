import { DataTypes, Model, Optional, Op } from 'sequelize';
import { userDatabase } from '../../config/database';
import { UserOTPAttributes } from '../../types';
import { User } from './User';

interface UserOTPCreationAttributes extends Optional<UserOTPAttributes, 'id' | 'createdAt'> {}

export class UserOTP extends Model<UserOTPAttributes, UserOTPCreationAttributes> implements UserOTPAttributes {
  public id!: string;
  public userId!: string;
  public otp!: string;
  public expiresAt!: Date;
  public isUsed!: boolean;
  public readonly createdAt!: Date;

  // Instance methods
  public isExpired(): boolean {
    return new Date() > this.expiresAt;
  }

  public async markAsUsed(): Promise<void> {
    this.isUsed = true;
    await this.save();
  }

  // Static methods
  static async createOTP(userId: string, otp: string, expiryMinutes: number = 5): Promise<UserOTP> {
    const { EncryptionUtil } = await import('../../utils/encryption');

    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + expiryMinutes);

    return UserOTP.create({
      id: EncryptionUtil.generateUUID(),
      userId,
      otp,
      expiresAt,
      isUsed: false,
    });
  }

  static async findValidOTP(userId: string, otp: string): Promise<UserOTP | null> {
    return UserOTP.findOne({
      where: {
        userId,
        otp,
        isUsed: false,
      },
    });
  }

  static async cleanupExpiredOTPs(): Promise<number> {
    const result = await UserOTP.destroy({
      where: {
        expiresAt: {
          [Op.lt]: new Date(),
        },
      },
    });
    return result;
  }

  static async cleanupUsedOTPs(): Promise<number> {
    const result = await UserOTP.destroy({
      where: {
        isUsed: true,
      },
    });
    return result;
  }
}

UserOTP.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    otp: {
      type: DataTypes.STRING(10),
      allowNull: false,
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    isUsed: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'UserOTP',
    tableName: 'user_otps',
    timestamps: false,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['otp'],
      },
      {
        fields: ['expires_at'],
      },
      {
        fields: ['is_used'],
      },
    ],
  }
);

// Define associations
User.hasMany(UserOTP, { foreignKey: 'user_id', as: 'otps' });
UserOTP.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
