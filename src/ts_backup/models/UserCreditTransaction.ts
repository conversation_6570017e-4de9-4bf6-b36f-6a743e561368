import { DataTypes, Model, Optional } from 'sequelize';
import { userDatabase } from '../../config/database';
import { UserCreditTransactionAttributes } from '../../types';
import { User } from './User';

interface UserCreditTransactionCreationAttributes extends Optional<UserCreditTransactionAttributes, 'id' | 'createdAt'> {}

export class UserCreditTransaction extends Model<UserCreditTransactionAttributes, UserCreditTransactionCreationAttributes> implements UserCreditTransactionAttributes {
  public id!: string;
  public userId!: string;
  public amount!: number;
  public type!: 'CREDIT' | 'DEBIT';
  public description!: string;
  public readonly createdAt!: Date;

  // Static methods
  static async createTransaction(data: {
    userId: string;
    amount: number;
    type: 'CREDIT' | 'DEBIT';
    description: string;
  }): Promise<UserCreditTransaction> {
    const { EncryptionUtil } = await import('../../utils/encryption');

    return UserCreditTransaction.create({
      id: EncryptionUtil.generateUUID(),
      userId: data.userId,
      amount: data.amount,
      type: data.type,
      description: data.description,
    });
  }

  static async getUserTransactions(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<UserCreditTransaction[]> {
    return UserCreditTransaction.findAll({
      where: { userId },
      order: [['createdAt', 'DESC']],
      limit,
      offset,
    });
  }

  static async getUserTransactionCount(userId: string): Promise<number> {
    return UserCreditTransaction.count({
      where: { userId },
    });
  }

  static async getTransactionsByType(
    userId: string,
    type: 'CREDIT' | 'DEBIT',
    limit: number = 50,
    offset: number = 0
  ): Promise<UserCreditTransaction[]> {
    return UserCreditTransaction.findAll({
      where: { userId, type },
      order: [['createdAt', 'DESC']],
      limit,
      offset,
    });
  }
}

UserCreditTransaction.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    amount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
      },
    },
    type: {
      type: DataTypes.ENUM('CREDIT', 'DEBIT'),
      allowNull: false,
    },
    description: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'UserCreditTransaction',
    tableName: 'user_credit_transactions',
    timestamps: false, // Only createdAt, no updatedAt
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['type'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['user_id', 'type'],
      },
      {
        fields: ['user_id', 'created_at'],
      },
    ],
  }
);

// Define associations
User.hasMany(UserCreditTransaction, { foreignKey: 'user_id', as: 'creditTransactions' });
UserCreditTransaction.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
