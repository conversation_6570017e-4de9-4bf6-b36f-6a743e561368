import { DataTypes, Model, Optional } from 'sequelize';
import { userDatabase } from '../../config/database';
import { UserCreditAttributes } from '../../types';
import { User } from './User';

interface UserCreditCreationAttributes extends Optional<UserCreditAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class UserCredit extends Model<UserCreditAttributes, UserCreditCreationAttributes> implements UserCreditAttributes {
  public id!: string;
  public userId!: string;
  public credits!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Instance methods
  public async addCredits(amount: number): Promise<void> {
    this.credits += amount;
    await this.save();
  }

  public async deductCredits(amount: number): Promise<boolean> {
    if (this.credits < amount) {
      return false; // Insufficient credits
    }
    this.credits -= amount;
    await this.save();
    return true;
  }

  public hasEnoughCredits(amount: number): boolean {
    return this.credits >= amount;
  }

  // Static methods
  static async createUserCredit(userId: string, initialCredits: number = 50): Promise<UserCredit> {
    const { EncryptionUtil } = await import('../../utils/encryption');

    return UserCredit.create({
      id: EncryptionUtil.generateUUID(),
      userId,
      credits: initialCredits,
    });
  }

  static async findByUserId(userId: string): Promise<UserCredit | null> {
    return UserCredit.findOne({
      where: { userId },
    });
  }

  static async findOrCreateByUserId(userId: string, initialCredits: number = 50): Promise<[UserCredit, boolean]> {
    const [userCredit, created] = await UserCredit.findOrCreate({
      where: { userId },
      defaults: {
        id: (await import('../../utils/encryption')).EncryptionUtil.generateUUID(),
        userId,
        credits: initialCredits,
      },
    });
    return [userCredit, created];
  }
}

UserCredit.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
      onDelete: 'CASCADE',
      unique: true, // One credit record per user
    },
    credits: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 50,
      validate: {
        min: 0,
      },
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'UserCredit',
    tableName: 'user_credits',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id'],
      },
      {
        fields: ['credits'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['updated_at'],
      },
    ],
  }
);

// Define associations
User.hasOne(UserCredit, { foreignKey: 'user_id', as: 'credits' });
UserCredit.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
