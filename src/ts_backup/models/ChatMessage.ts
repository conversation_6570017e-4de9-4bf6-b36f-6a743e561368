import { DataTypes, Model, Optional, Op } from 'sequelize';
import { chatDatabase } from '../../config/database';
import { ChatMessageAttributes } from '../../types';
import { Chat } from './Chat';
import { ChatThread } from './ChatThread';

interface ChatMessageCreationAttributes extends Optional<ChatMessageAttributes, 'id' | 'createdAt'> {}

export class ChatMessage extends Model<ChatMessageAttributes, ChatMessageCreationAttributes> implements ChatMessageAttributes {
  public id!: string;
  public chatId!: string;
  public message!: string;
  public response!: string;
  public llmModel!: string;
  public isUserMessage!: boolean;
  public parentMessageId?: string;
  public version!: number;
  public isRegenerated!: boolean;
  public attachmentPath?: string;
  public attachmentName?: string;
  public attachmentType?: string;
  public attachmentSize?: number;
  public readonly createdAt!: Date;

  // Static methods
  static async createMessage(data: {
    chatId: string;
    message: string;
    response: string;
    llmModel: string;
    isUserMessage?: boolean;
    parentMessageId?: string;
    version?: number;
    isRegenerated?: boolean;
    attachmentPath?: string;
    attachmentName?: string;
    attachmentType?: string;
    attachmentSize?: number;
  }): Promise<ChatMessage> {
    const { EncryptionUtil } = await import('../../utils/encryption');

    return ChatMessage.create({
      id: EncryptionUtil.generateUUID(),
      chatId: data.chatId,
      message: data.message,
      response: data.response,
      llmModel: data.llmModel,
      isUserMessage: data.isUserMessage ?? true,
      parentMessageId: data.parentMessageId,
      version: data.version ?? 1,
      isRegenerated: data.isRegenerated ?? false,
      attachmentPath: data.attachmentPath,
      attachmentName: data.attachmentName,
      attachmentType: data.attachmentType,
      attachmentSize: data.attachmentSize,
    });
  }

  static async findChatMessages(chatId: string, limit: number = 50, offset: number = 0): Promise<ChatMessage[]> {
    return ChatMessage.findAll({
      where: { chatId },
      order: [['createdAt', 'ASC']],
      limit,
      offset,
    });
  }

  static async findLatestMessage(chatId: string): Promise<ChatMessage | null> {
    return ChatMessage.findOne({
      where: { chatId },
      order: [['createdAt', 'DESC']],
    });
  }

  static async countChatMessages(chatId: string): Promise<number> {
    return ChatMessage.count({
      where: { chatId },
    });
  }

  static async deleteChatMessages(chatId: string): Promise<number> {
    return ChatMessage.destroy({
      where: { chatId },
    });
  }

  static async findById(messageId: string): Promise<ChatMessage | null> {
    return ChatMessage.findByPk(messageId);
  }

  static async findMessageVersions(parentMessageId: string): Promise<ChatMessage[]> {
    return ChatMessage.findAll({
      where: {
        [Op.or]: [
          { id: parentMessageId },
          { parentMessageId: parentMessageId }
        ]
      },
      order: [['version', 'ASC'], ['createdAt', 'ASC']],
    });
  }

  static async getLatestVersion(parentMessageId: string): Promise<number> {
    const latestMessage = await ChatMessage.findOne({
      where: {
        [Op.or]: [
          { id: parentMessageId },
          { parentMessageId: parentMessageId }
        ]
      },
      order: [['version', 'DESC']],
    });

    return latestMessage ? latestMessage.version : 0;
  }

  static async createRegeneratedResponse(data: {
    originalMessageId: string;
    newResponse: string;
    llmModel: string;
  }): Promise<ChatMessage> {
    const originalMessage = await ChatMessage.findById(data.originalMessageId);
    if (!originalMessage) {
      throw new Error('Original message not found');
    }

    const { EncryptionUtil } = await import('../../utils/encryption');
    const nextVersion = await ChatMessage.getLatestVersion(data.originalMessageId) + 1;

    return ChatMessage.create({
      id: EncryptionUtil.generateUUID(),
      chatId: originalMessage.chatId,
      message: originalMessage.message,
      response: data.newResponse,
      llmModel: data.llmModel,
      isUserMessage: originalMessage.isUserMessage,
      parentMessageId: data.originalMessageId,
      version: nextVersion,
      isRegenerated: true,
    });
  }

  static async findMessagesByDateRange(
    chatId: string,
    startDate: Date,
    endDate: Date
  ): Promise<ChatMessage[]> {
    return ChatMessage.findAll({
      where: {
        chatId,
        createdAt: {
          [Op.between]: [startDate, endDate],
        },
      },
      order: [['createdAt', 'ASC']],
    });
  }
}

ChatMessage.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    chatId: {
      type: DataTypes.UUID,
      allowNull: false,
      // No foreign key constraint - application logic ensures referential integrity
      // This allows flexibility for both Chat and ChatThread IDs
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    response: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    llmModel: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    isUserMessage: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    parentMessageId: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    version: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    isRegenerated: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    attachmentPath: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
    attachmentName: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    attachmentType: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    attachmentSize: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: chatDatabase,
    modelName: 'ChatMessage',
    tableName: 'chat_messages',
    timestamps: false,
    indexes: [
      {
        fields: ['chat_id'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['llm_model'],
      },
      {
        fields: ['is_user_message'],
      },
      {
        fields: ['parent_message_id'],
      },
      {
        fields: ['version'],
      },
      {
        fields: ['is_regenerated'],
      },
      {
        fields: ['attachment_path'],
      },
      {
        fields: ['attachment_type'],
      },
    ],
  }
);

// Define logical associations without foreign key constraints
// This prevents conflicts since ChatMessage can reference both Chat and ChatThread
Chat.hasMany(ChatMessage, {
  foreignKey: 'chat_id',
  as: 'messages',
  constraints: false // Disable foreign key constraint
});
ChatMessage.belongsTo(Chat, {
  foreignKey: 'chat_id',
  as: 'chat',
  constraints: false // Disable foreign key constraint
});

// Also support ChatThread associations
ChatThread.hasMany(ChatMessage, {
  foreignKey: 'chat_id',
  as: 'messages',
  constraints: false // Disable foreign key constraint
});
ChatMessage.belongsTo(ChatThread, {
  foreignKey: 'chat_id',
  as: 'thread',
  constraints: false // Disable foreign key constraint
});
