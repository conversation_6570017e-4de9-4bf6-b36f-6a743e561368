import { User } from './User.js';
import { UserCredit } from './UserCredit.js';
import { UserCreditTransaction } from './UserCreditTransaction.js';
import { UserOTP } from './UserOTP.js';
import { UserProfile } from './UserProfile.js';

/**
 * Define model associations for user-related models
 * This file sets up foreign key relationships between models
 */

/**
 * Set up all model associations
 */
export function setupUserAssociations() {
  // User has one UserCredit
  User.hasOne(UserCredit, {
    foreignKey: 'userId',
    as: 'credits',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
  });

  // UserCredit belongs to User
  UserCredit.belongsTo(User, {
    foreignKey: 'userId',
    as: 'user',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
  });

  // User has many UserCreditTransactions
  User.hasMany(UserCreditTransaction, {
    foreignKey: 'userId',
    as: 'creditTransactions',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
  });

  // UserCreditTransaction belongs to User
  UserCreditTransaction.belongsTo(User, {
    foreignKey: 'userId',
    as: 'user',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
  });

  // User has many UserOTPs
  User.hasMany(UserOTP, {
    foreignKey: 'userId',
    as: 'otps',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
  });

  // UserOTP belongs to User
  UserOTP.belongsTo(User, {
    foreignKey: 'userId',
    as: 'user',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
  });

  // User has one UserProfile
  User.hasOne(UserProfile, {
    foreignKey: 'userId',
    as: 'profile',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
  });

  // UserProfile belongs to User
  UserProfile.belongsTo(User, {
    foreignKey: 'userId',
    as: 'user',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
  });
}
