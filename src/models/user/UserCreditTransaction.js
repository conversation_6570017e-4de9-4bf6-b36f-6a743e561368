import { DataTypes, Model, Op  } from 'sequelize';
import { userDatabase  } from '../../config/database.js';

import { EncryptionUtil  } from '../../utils/encryption.js';

/**
 * UserCreditTransaction Model
 * Records all credit transactions for audit and history tracking
 */
class UserCreditTransaction extends Model {
  /**
   * Create a new credit transaction
   * @param {Object} data - Transaction data
   * @param {string} data.userId - User ID
   * @param {number} data.amount - Transaction amount
   * @param {'CREDIT'|'DEBIT'} data.type - Transaction type
   * @param {string} data.description - Transaction description
   * @returns {Promise<UserCreditTransaction>} Created transaction
   */
  static async createTransaction(data) {

    return UserCreditTransaction.create({
      id: EncryptionUtil.generateUUID(),
      userId: data.userId,
      amount: data.amount,
      type: data.type,
      description: data.description,
    });
  }

  /**
   * Get user transaction history
   * @param {string} userId - User ID
   * @param {number} [limit=50] - Limit number of results
   * @param {number} [offset=0] - Offset for pagination
   * @returns {Promise<UserCreditTransaction[]>} Array of transactions
   */
  static async getUserTransactions(userId, limit = 50, offset = 0) {
    return UserCreditTransaction.findAll({
      where: { userId },
      order: [['createdAt', 'DESC']],
      limit,
      offset,
    });
  }

  /**
   * Get transactions by type
   * @param {string} userId - User ID
   * @param {'CREDIT'|'DEBIT'} type - Transaction type
   * @param {number} [limit=50] - Limit number of results
   * @returns {Promise<UserCreditTransaction[]>} Array of transactions
   */
  static async getTransactionsByType(userId, type, limit = 50) {
    return UserCreditTransaction.findAll({
      where: { userId, type },
      order: [['createdAt', 'DESC']],
      limit,
    });
  }

  /**
   * Get transactions within date range
   * @param {string} userId - User ID
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Promise<UserCreditTransaction[]>} Array of transactions
   */
  static async getTransactionsByDateRange(userId, startDate, endDate) {
    return UserCreditTransaction.findAll({
      where: {
        userId,
        createdAt: {
          [Op.between]: [startDate, endDate],
        },
      },
      order: [['createdAt', 'DESC']],
    });
  }

  /**
   * Calculate total credits earned
   * @param {string} userId - User ID
   * @returns {Promise<number>} Total credits earned
   */
  static async getTotalCreditsEarned(userId) {
    const result = await UserCreditTransaction.sum('amount', {
      where: {
        userId,
        type: 'CREDIT',
      },
    });
    return result || 0;
  }

  /**
   * Calculate total credits spent
   * @param {string} userId - User ID
   * @returns {Promise<number>} Total credits spent
   */
  static async getTotalCreditsSpent(userId) {
    const result = await UserCreditTransaction.sum('amount', {
      where: {
        userId,
        type: 'DEBIT',
      },
    });
    return result || 0;
  }

  /**
   * Get transaction summary for user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Transaction summary
   */
  static async getTransactionSummary(userId) {
    const [totalEarned, totalSpent, transactionCount] = await Promise.all([
      this.getTotalCreditsEarned(userId),
      this.getTotalCreditsSpent(userId),
      UserCreditTransaction.count({ where: { userId } }),
    ]);

    return {
      totalEarned,
      totalSpent,
      netCredits: totalEarned - totalSpent,
      transactionCount,
    };
  }
}

UserCreditTransaction.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      // Note: Foreign key reference will be set up in associations
    },
    amount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 0,
      },
    },
    type: {
      type: DataTypes.ENUM('CREDIT', 'DEBIT'),
      allowNull: false,
    },
    description: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'UserCreditTransaction',
    tableName: 'user_credit_transactions',
    timestamps: false,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['type'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['amount'],
      },
    ],
  }
);

export { UserCreditTransaction  };
